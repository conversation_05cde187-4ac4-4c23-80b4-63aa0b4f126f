<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>QLS Verzendlabel Generator</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
<h1>QLS Verzendlabel Generator</h1>

<div class="main-container">
    <h2>Bestelling #958201</h2>

    <div class="order-info">
        <p><strong>Klant:</strong> <PERSON></p>
        <p><strong>Bezorgadres:</strong>
            Daltonstraat 65            ,
            3316GD Dordrecht        </p>
    </div>

    <table>
        <thead>
        <tr>
            <th>Aantal</th>
            <th>Artikel</th>
            <th>SKU</th>
            <th>EAN</th>
        </tr>
        </thead>
        <tbody>
                    <tr>
                <td>2</td>
                <td>Jeans - Black - 36</td>
                <td>69205</td>
                <td>8710552295268</td>
            </tr>
                    <tr>
                <td>1</td>
                <td>Sjaal - Rood Oranje</td>
                <td>25920</td>
                <td>3059943009097</td>
            </tr>
                </tbody>
    </table>
</div>

<div class="main-container">
    <h2>Verzendlabel Genereren</h2>

    <div>
        <p>Verzending via DHL Pakje. Na het klikken op de knop wordt er een PDF gemaakt met zowel de pakbon
            als verzendlabel.</p>

                    <div id="status-message">
                Kon geen verzendlabel aanmaken. Probeer het later opnieuw.            </div>
        
        <form method="post">
            <input type="hidden" name="product_id" value="3">
            <button type="submit">Genereer Pakbon + Label</button>
        </form>
    </div>
</div>
</body>
</html>
